<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knead & Nourish - Coming Soon</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@300;400&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="content">
            <div class="coming-soon">
                <h2>Coming Soon</h2>              
            </div>
            <div class="logo-container">
                <!-- Replace this with your actual logo -->
                <img src="logo.png" alt="Knead & Nourish Logo" class="logo" id="logo">
                <!-- Fallback text logo if image doesn't load -->
                <div class="text-logo" id="textLogo">
                    <h1>Knead & Nourish</h1>
                </div>
                <p>We're baking up something special for you!</p>
            </div>

        </div>
    </div>

    <!-- Debug panel for rain system -->
    <div class="debug-panel expanded" id="debugPanel" onclick="toggleDebugPanel()">
        <div class="debug-panel-icon" id="debugPanelIcon">🌧️</div>
        <div class="debug-panel-content" id="debugPanelContent" onclick="event.stopPropagation()">
            <h3>Rain Debug</h3>
            <div class="debug-info">
                <div>
                    <span class="debug-label">Status:</span>
                    <span class="debug-value" id="rainStatus">Inactive</span>
                </div>
                <div>
                    <span class="debug-label">Current Intensity:</span>
                    <span class="debug-value" id="currentIntensity">None</span>
                </div>
                <div>
                    <span class="debug-label">Peak Intensity:</span>
                    <span class="debug-value" id="peakIntensity">None</span>
                </div>
                <div>
                    <span class="debug-label">Event Duration:</span>
                    <span class="debug-value" id="eventDuration">0s</span>
                </div>
                <div>
                    <span class="debug-label">Time Elapsed:</span>
                    <span class="debug-value" id="timeElapsed">0s</span>
                </div>
                <div>
                    <span class="debug-label">Next Rain In:</span>
                    <span class="debug-value" id="nextRainIn">--</span>
                </div>
                <div>
                    <span class="debug-label">Fade Duration:</span>
                    <span class="debug-value" id="fadeDuration">--</span>
                </div>
                <div>
                    <span class="debug-label">Droplets on Screen:</span>
                    <span class="debug-value" id="dropletsCount">0</span>
                </div>
                <div>
                    <span class="debug-label">Target Range:</span>
                    <span class="debug-value" id="targetRange">--</span>
                </div>
                <div>
                    <span class="debug-label">Current Target:</span>
                    <span class="debug-value" id="currentTarget">--</span>
                </div>
            </div>
            <button class="debug-button" id="triggerRainBtn" onclick="triggerDebugRain()">
                Trigger Rain Event
            </button>
        </div>
    </div>

    <!-- Rain container - rain drops will be generated dynamically -->
    <div class="rain-container" id="rainContainer">
    </div>

    <!-- Wheat stalks container - stalks will be generated dynamically based on screen width -->
    <div class="wheat-field" id="wheatField">
    </div>

    <script>
        // Show text logo if image fails to load
        function showTextLogo() {
            const logoImg = document.getElementById('logo');
            const textLogo = document.getElementById('textLogo');

            if (logoImg) {
                logoImg.style.display = 'none';
            }
            if (textLogo) {
                textLogo.style.display = 'block';
            }
        }

        // Set up the error handler
        document.addEventListener('DOMContentLoaded', function() {
            const logoImg = document.getElementById('logo');
            if (logoImg) {
                logoImg.onerror = showTextLogo;

                // Also check if the image is already broken (in case the error fired before we set the handler)
                if (logoImg.complete && logoImg.naturalWidth === 0) {
                    showTextLogo();
                }
            }
        });

        // Dynamic wheat field generation and animation
        let mouseX = window.innerWidth / 2; // Start at center
        let mouseY = window.innerHeight; // Start at bottom (full growth)
        let wheatGrowthFactor = 1.0; // Growth factor from 0 (seed) to 1 (full height)
        let wheatStalks = [];
        let stalkData = []; // Store stalk properties
        let gustSystems = [
            { active: false, position: 0, direction: 1, strength: 0, width: 15, speed: 2, nextGustTime: 0, id: 0 },
            { active: false, position: 0, direction: 1, strength: 0, width: 15, speed: 2, nextGustTime: 0, id: 1 },
            { active: false, position: 0, direction: 1, strength: 0, width: 15, speed: 2, nextGustTime: 0, id: 2 }
        ];
        let gustFrequency = {
            baseInterval: 2000, // Base 2 seconds
            variationRange: 4000, // +0 to +4 seconds variation
            lastGustTime: 0,
            minStagger: 800 // Minimum 800ms between gust starts
        };

        // Rain system variables
        let rainSystem = {
            active: false,
            intensity: 'light', // 'light', 'moderate', 'heavy'
            fadingIn: false,
            fadingOut: false,
            nextRainTime: 0,
            rainDuration: 0,
            rainDrops: [],
            maxDrops: 800, // Maximum number of rain drops on screen (increased for 2x frequency)
            dropPool: [], // Pool of reusable rain drop elements
            lastDropTime: 0,
            dropInterval: 50, // Base interval between drops in ms

            // Debug tracking variables
            eventStartTime: 0,
            peakIntensity: 'none',
            isDebugTriggered: false,

            // Dynamic fade durations
            fadeInDuration: 3000,
            fadeOutDuration: 3000,

            // Current target droplet count for debugging
            currentTargetDroplets: 0
        };

        let rainTiming = {
            minCyclePause: 15000, // 15 seconds minimum between rain cycles
            maxCyclePause: 60000, // 60 seconds maximum between rain cycles
            minRainDuration: 15000, // 15 seconds minimum rain duration
            maxRainDuration: 180000, // 180 seconds (3 minutes) maximum rain duration
            // Fade durations are now calculated dynamically based on event duration
        };

        // Calculate wheat growth factor based on vertical mouse position
        function calculateWheatGrowth(mouseY) {
            const windowHeight = window.innerHeight;
            const seedZone = windowHeight * 0.05; // Bottom 5% is seed zone (no growth)
            const maxGrowthZone = windowHeight * 0.5; // Middle 50% is max growth zone

            // If mouse is in bottom 5%, wheat is seed (not visible)
            if (mouseY >= windowHeight - seedZone) {
                return 0;
            }

            // If mouse is at or above middle (50%), wheat is at max height
            if (mouseY <= maxGrowthZone) {
                return 1;
            }

            // Calculate growth factor between seed zone and max growth zone
            const growthRange = (windowHeight - seedZone) - maxGrowthZone;
            const currentPosition = mouseY - maxGrowthZone;
            const growthFactor = 1 - (currentPosition / growthRange);

            return Math.max(0, Math.min(1, growthFactor));
        }

        // Generate wheat stalks based on screen width
        function generateWheatStalks() {
            const wheatField = document.getElementById('wheatField');
            const windowWidth = window.innerWidth;

            // Calculate number of stalks based on screen width (tripled for denser field)
            let stalkCount;
            if (windowWidth >= 1920) {
                stalkCount = 450; // Ultra-wide screens (150 x 3)
            } else if (windowWidth >= 1440) {
                stalkCount = 360; // Large screens (120 x 3)
            } else if (windowWidth >= 1024) {
                stalkCount = 300; // Desktop (100 x 3)
            } else if (windowWidth >= 768) {
                stalkCount = 240; // Tablet (80 x 3)
            } else if (windowWidth >= 480) {
                stalkCount = 180; // Mobile (60 x 3)
            } else {
                stalkCount = 120; // Small mobile (40 x 3)
            }

            // Clear existing stalks
            wheatField.innerHTML = '';
            wheatStalks = [];
            stalkData = [];

            // Generate height variations
            const heights = [68, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95];

            // Create stalks with four-segment structure for realistic bending
            for (let i = 0; i < stalkCount; i++) {
                const stalk = document.createElement('div');
                stalk.className = 'wheat-stalk';
                stalk.setAttribute('data-index', i);

                // Assign random height
                const randomHeight = heights[Math.floor(Math.random() * heights.length)];
                stalk.style.height = randomHeight + 'px';

                // Create four segments for progressive bending
                const baseSegment = document.createElement('div');
                baseSegment.className = 'wheat-segment wheat-base';

                const lowerMiddleSegment = document.createElement('div');
                lowerMiddleSegment.className = 'wheat-segment wheat-lower-middle';

                const upperMiddleSegment = document.createElement('div');
                upperMiddleSegment.className = 'wheat-segment wheat-upper-middle';

                const topSegment = document.createElement('div');
                topSegment.className = 'wheat-segment wheat-top';

                // Calculate segment heights (base: 30%, lower-middle: 25%, upper-middle: 25%, top: 20%)
                const baseHeight = Math.floor(randomHeight * 0.3);
                const lowerMiddleHeight = Math.floor(randomHeight * 0.25);
                const upperMiddleHeight = Math.floor(randomHeight * 0.25);
                const topHeight = randomHeight - baseHeight - lowerMiddleHeight - upperMiddleHeight;

                // Set heights and positions for each segment
                baseSegment.style.height = baseHeight + 'px';
                baseSegment.style.bottom = '0px';

                lowerMiddleSegment.style.height = lowerMiddleHeight + 'px';
                lowerMiddleSegment.style.bottom = baseHeight + 'px';

                upperMiddleSegment.style.height = upperMiddleHeight + 'px';
                upperMiddleSegment.style.bottom = (baseHeight + lowerMiddleHeight) + 'px';

                topSegment.style.height = topHeight + 'px';
                topSegment.style.bottom = (baseHeight + lowerMiddleHeight + upperMiddleHeight) + 'px';

                // Set transform origins for chained movement
                // Base segment pivots from its bottom center (stalk root)
                baseSegment.style.transformOrigin = 'bottom center';

                // Middle and top segments will pivot from their bottom center since we're using translate to position them
                lowerMiddleSegment.style.transformOrigin = 'bottom center';
                upperMiddleSegment.style.transformOrigin = 'bottom center';
                topSegment.style.transformOrigin = 'bottom center';

                // Add all segments as siblings to the stalk container
                stalk.appendChild(baseSegment);
                stalk.appendChild(lowerMiddleSegment);
                stalk.appendChild(upperMiddleSegment);
                stalk.appendChild(topSegment);

                // Store stalk data for wind calculations
                const stalkInfo = {
                    element: stalk,
                    baseSegment: baseSegment,
                    lowerMiddleSegment: lowerMiddleSegment,
                    upperMiddleSegment: upperMiddleSegment,
                    topSegment: topSegment,
                    height: randomHeight,
                    baseHeight: baseHeight,
                    lowerMiddleHeight: lowerMiddleHeight,
                    upperMiddleHeight: upperMiddleHeight,
                    topHeight: topHeight,
                    flexibility: randomHeight / 95, // Taller stalks are more flexible (0.7 to 1.0)
                    naturalSway: Math.random() * 0.3 + 0.8, // Individual character (0.8 to 1.1)
                    position: i / stalkCount // Position across screen (0 to 1)
                };

                wheatField.appendChild(stalk);
                wheatStalks.push(stalk);
                stalkData.push(stalkInfo);
            }
        }

        // Mouse tracking
        document.addEventListener('mousemove', function(e) {
            mouseX = e.clientX;
            mouseY = e.clientY;
            wheatGrowthFactor = calculateWheatGrowth(mouseY);
        });

        // Touch tracking for mobile devices
        let isTouch = false;

        document.addEventListener('touchstart', function(e) {
            isTouch = true;
            if (e.touches.length > 0) {
                mouseX = e.touches[0].clientX;
                mouseY = e.touches[0].clientY;
                wheatGrowthFactor = calculateWheatGrowth(mouseY);
            }
            // Prevent default to avoid scrolling and other touch behaviors
            e.preventDefault();
        }, { passive: false });

        document.addEventListener('touchmove', function(e) {
            if (e.touches.length > 0) {
                mouseX = e.touches[0].clientX;
                mouseY = e.touches[0].clientY;
                wheatGrowthFactor = calculateWheatGrowth(mouseY);
            }
            // Prevent default to avoid scrolling and other touch behaviors
            e.preventDefault();
        }, { passive: false });

        document.addEventListener('touchend', function(e) {
            // Return horizontal position to center/neutral when finger is lifted
            mouseX = window.innerWidth / 2;
            // Keep vertical position and growth factor at last touch position
            // (wheat height remains at whatever height it was when touch ended)
            // Prevent default to avoid unwanted click events
            e.preventDefault();
        }, { passive: false });

        // Reset touch flag when mouse is used (for hybrid devices)
        document.addEventListener('mouseenter', function() {
            isTouch = false;
        });

        // Enhanced gust system management with multiple simultaneous gusts
        function updateGustSystems() {
            const currentTime = Date.now();

            // Check each gust system
            gustSystems.forEach(gust => {
                // Try to start a new gust if this one is inactive and it's time
                if (!gust.active && currentTime >= gust.nextGustTime) {
                    // Check if enough time has passed since the last gust started (stagger requirement)
                    if (currentTime - gustFrequency.lastGustTime >= gustFrequency.minStagger) {
                        // Start a new gust with varied characteristics
                        gust.active = true;

                        // Vary direction (60% left-to-right, 40% right-to-left for natural feel)
                        const startFromLeft = Math.random() < 0.6;
                        gust.direction = startFromLeft ? 1 : -1;
                        gust.position = startFromLeft ? -25 : 125; // Start further off-screen

                        // Vary strength significantly (0.2 to 1.2 for dramatic range)
                        gust.strength = Math.random() * 1.0 + 0.2;

                        // Vary width based on strength (stronger gusts are wider)
                        const baseWidth = 15 + (gust.strength * 20); // 15-35 stalks
                        gust.width = baseWidth + (Math.random() * 10 - 5); // ±5 variation

                        // Speed correlates with strength (stronger = faster)
                        const baseSpeed = 0.8 + (gust.strength * 2.2); // 1.0 to 3.0
                        gust.speed = baseSpeed + (Math.random() * 0.8 - 0.4); // ±0.4 variation

                        // Set next gust time with high variation
                        const nextInterval = gustFrequency.baseInterval + (Math.random() * gustFrequency.variationRange);
                        gust.nextGustTime = currentTime + nextInterval;

                        // Update last gust start time for staggering
                        gustFrequency.lastGustTime = currentTime;
                    }
                }

                // Update active gusts
                if (gust.active) {
                    // Move the gust across the screen
                    gust.position += gust.direction * gust.speed;

                    // Check if gust has passed through completely
                    const exitThreshold = gust.direction > 0 ? 125 : -25;
                    if ((gust.direction > 0 && gust.position > exitThreshold) ||
                        (gust.direction < 0 && gust.position < exitThreshold)) {
                        gust.active = false;
                    }
                }
            });
        }

        // Rain system functions
        function initializeRainSystem() {
            const rainContainer = document.getElementById('rainContainer');

            // Pre-create rain drop elements for performance
            for (let i = 0; i < rainSystem.maxDrops; i++) {
                const drop = document.createElement('div');
                drop.className = 'rain-drop';
                drop.style.display = 'none';
                rainContainer.appendChild(drop);
                rainSystem.dropPool.push(drop);
            }



            // Set initial rain timing
            const currentTime = Date.now();
            const initialDelay = Math.random() * (rainTiming.maxCyclePause - rainTiming.minCyclePause) + rainTiming.minCyclePause;
            rainSystem.nextRainTime = currentTime + initialDelay;
        }

        function updateRainSystem() {
            const currentTime = Date.now();
            const rainContainer = document.getElementById('rainContainer');

            // Check if it's time to start a new rain cycle
            if (!rainSystem.active && !rainSystem.fadingIn && !rainSystem.fadingOut && currentTime >= rainSystem.nextRainTime) {
                startRainCycle();
            }

            // Handle rain cycle phases
            if (rainSystem.active || rainSystem.fadingIn || rainSystem.fadingOut) {
                // Generate rain drops during active phase AND fade periods
                if ((rainSystem.active || rainSystem.fadingIn || rainSystem.fadingOut) &&
                    currentTime - rainSystem.lastDropTime >= getDropInterval()) {
                    createRainDrop();
                    rainSystem.lastDropTime = currentTime;
                }

                // Check if rain duration is complete and start fade out
                if (rainSystem.active && currentTime >= rainSystem.nextRainTime + rainSystem.rainDuration) {
                    startRainFadeOut();
                }
            }

            // Clean up finished rain drops and splashes
            cleanupRainElements();
        }

        function startRainCycle() {
            const currentTime = Date.now();
            const rainContainer = document.getElementById('rainContainer');

            // Set random rain duration first
            rainSystem.rainDuration = Math.random() * (rainTiming.maxRainDuration - rainTiming.minRainDuration) + rainTiming.minRainDuration;

            // Calculate fade durations based on event duration
            const fadeDurations = calculateFadeDurations(rainSystem.rainDuration);
            rainSystem.fadeInDuration = fadeDurations.fadeIn;
            rainSystem.fadeOutDuration = fadeDurations.fadeOut;

            // Set intensity based on duration
            rainSystem.intensity = getIntensityForDuration(rainSystem.rainDuration);

            // Debug tracking
            rainSystem.eventStartTime = currentTime;
            rainSystem.peakIntensity = rainSystem.intensity; // Track peak intensity for this event

            // Update CSS animation durations
            rainContainer.style.setProperty('--fade-in-duration', rainSystem.fadeInDuration + 'ms');
            rainContainer.style.setProperty('--fade-out-duration', rainSystem.fadeOutDuration + 'ms');

            // Start fade in
            rainSystem.fadingIn = true;
            rainContainer.classList.add('fading-in');
            rainContainer.classList.remove('fading-out');

            // Set drop interval based on intensity
            updateDropInterval();

            // After fade in duration, set to active
            setTimeout(() => {
                rainSystem.fadingIn = false;
                rainSystem.active = true;
                rainContainer.classList.remove('fading-in');
            }, rainSystem.fadeInDuration);
        }

        function startRainFadeOut() {
            const rainContainer = document.getElementById('rainContainer');

            rainSystem.active = false;
            rainSystem.fadingOut = true;
            rainContainer.classList.add('fading-out');
            rainContainer.classList.remove('fading-in');

            // After fade out duration, end rain cycle
            setTimeout(() => {
                endRainCycle();
            }, rainSystem.fadeOutDuration);
        }

        function endRainCycle() {
            const currentTime = Date.now();
            const rainContainer = document.getElementById('rainContainer');

            rainSystem.fadingOut = false;
            rainContainer.classList.remove('fading-out');

            // Hide all remaining rain drops
            rainSystem.dropPool.forEach(drop => {
                drop.style.display = 'none';
                drop.style.animation = '';
            });

            // Schedule next rain cycle (shorter delay if debug triggered)
            let nextCyclePause;
            if (rainSystem.isDebugTriggered) {
                nextCyclePause = 5000; // 5 seconds after debug trigger
            } else {
                nextCyclePause = Math.random() * (rainTiming.maxCyclePause - rainTiming.minCyclePause) + rainTiming.minCyclePause;
            }
            rainSystem.nextRainTime = currentTime + nextCyclePause;

            // Reset debug tracking for next cycle
            rainSystem.eventStartTime = 0;
            rainSystem.isDebugTriggered = false;
        }

        function createRainDrop() {
            // Get available rain drop from pool
            const availableDrop = rainSystem.dropPool.find(drop => drop.style.display === 'none');
            if (!availableDrop) return; // No available drops

            // Set rain drop properties - all drops are the same size now
            availableDrop.className = 'rain-drop';
            availableDrop.style.display = 'block';

            // Apply fade effects during fade periods
            let dropOpacity = 1.0;
            if (rainSystem.fadingIn) {
                // Calculate fade-in progress (0 to 1)
                const fadeProgress = Math.min(1, (Date.now() - rainSystem.eventStartTime) / rainSystem.fadeInDuration);
                dropOpacity = fadeProgress;
            } else if (rainSystem.fadingOut) {
                // Calculate fade-out progress (1 to 0)
                const fadeStartTime = rainSystem.eventStartTime + rainSystem.rainDuration;
                const fadeProgress = Math.min(1, (Date.now() - fadeStartTime) / rainSystem.fadeOutDuration);
                dropOpacity = 1 - fadeProgress;
            }
            availableDrop.style.opacity = dropOpacity;

            // Random horizontal position
            const windowWidth = window.innerWidth;
            const leftPosition = Math.random() * windowWidth;
            availableDrop.style.left = leftPosition + 'px';

            // Consistent fall duration - terminal velocity never changes
            const fallDuration = 1000 + Math.random() * 200; // 1.0-1.2s consistent speed

            // Check if there's wind influence from gust systems
            let hasWindInfluence = false;
            gustSystems.forEach(gust => {
                if (gust.active) {
                    const dropPosition = (leftPosition / windowWidth) * 100;
                    const distanceFromGust = Math.abs(dropPosition - gust.position);
                    if (distanceFromGust <= gust.width) {
                        hasWindInfluence = true;
                    }
                }
            });

            // Apply animation
            const animationName = hasWindInfluence ? 'rainFallWithWind' : 'rainFall';
            availableDrop.style.animation = `${animationName} ${fallDuration}ms linear forwards`;

            // Hide the drop immediately when it reaches the bottom (no splash)
            setTimeout(() => {
                availableDrop.style.display = 'none';
                availableDrop.style.animation = '';
            }, fallDuration);
        }



        function getDropInterval() {
            // Calculate target droplet count based on intensity percentage ranges
            const maxDrops = rainSystem.maxDrops;
            let targetDropletCount;

            switch (rainSystem.intensity) {
                case 'very-light':
                    // 0-20% of max droplets (0-160 droplets at 800 max)
                    targetDropletCount = (maxDrops * 0.05) + Math.random() * (maxDrops * 0.15); // 5-20%
                    break;
                case 'light':
                    // 21-40% of max droplets (168-320 droplets at 800 max)
                    targetDropletCount = (maxDrops * 0.21) + Math.random() * (maxDrops * 0.19); // 21-40%
                    break;
                case 'moderate':
                    // 41-60% of max droplets (328-480 droplets at 800 max)
                    targetDropletCount = (maxDrops * 0.41) + Math.random() * (maxDrops * 0.19); // 41-60%
                    break;
                case 'heavy':
                    // 61-80% of max droplets (488-640 droplets at 800 max)
                    targetDropletCount = (maxDrops * 0.61) + Math.random() * (maxDrops * 0.19); // 61-80%
                    break;
                case 'very-heavy':
                    // 81-100% of max droplets (648-800 droplets at 800 max)
                    targetDropletCount = (maxDrops * 0.81) + Math.random() * (maxDrops * 0.19); // 81-100%
                    break;
                default:
                    targetDropletCount = maxDrops * 0.1; // Fallback
            }

            // Calculate interval based on target droplet count and actual droplet lifetime
            // Droplets live for fall duration only (no splash effects)
            const fallDuration = 1100; // 1.0-1.2s average fall time
            const totalDropletLifetime = fallDuration; // Droplets disappear immediately at bottom

            // To maintain targetDropletCount on screen, we need to spawn:
            // targetDropletCount / (totalDropletLifetime / 1000) droplets per second
            const dropletsPerSecond = targetDropletCount / (totalDropletLifetime / 1000);
            const optimalInterval = 1000 / dropletsPerSecond;

            // Store current target for debugging
            rainSystem.currentTargetDroplets = Math.round(targetDropletCount);

            // Ensure minimum interval for performance and add slight randomness
            const minInterval = 3; // Minimum 3ms between drops
            const randomVariation = Math.random() * 10 - 5; // ±5ms variation

            return Math.max(minInterval, optimalInterval + randomVariation);
        }

        function updateDropInterval() {
            // This function is called when rain intensity changes
            // The actual interval is calculated in getDropInterval()
        }

        function cleanupRainElements() {
            // This function could be used for additional cleanup if needed
            // Currently, cleanup is handled in the animation timeouts
        }

        function countActiveDroplets() {
            // Count droplets that are currently visible on screen
            return rainSystem.dropPool.filter(drop => drop.style.display !== 'none').length;
        }

        function getTargetDropletRange(intensity) {
            // Get the target droplet range for a given intensity
            const maxDrops = rainSystem.maxDrops;

            switch (intensity) {
                case 'very-light':
                    return { min: Math.floor(maxDrops * 0.05), max: Math.floor(maxDrops * 0.2) }; // 5-20%
                case 'light':
                    return { min: Math.floor(maxDrops * 0.21), max: Math.floor(maxDrops * 0.4) }; // 21-40%
                case 'moderate':
                    return { min: Math.floor(maxDrops * 0.41), max: Math.floor(maxDrops * 0.6) }; // 41-60%
                case 'heavy':
                    return { min: Math.floor(maxDrops * 0.61), max: Math.floor(maxDrops * 0.8) }; // 61-80%
                case 'very-heavy':
                    return { min: Math.floor(maxDrops * 0.81), max: maxDrops }; // 81-100%
                default:
                    return { min: 0, max: maxDrops };
            }
        }

        function getWeightedRandomChoice(choices, weights) {
            const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
            let random = Math.random() * totalWeight;

            for (let i = 0; i < choices.length; i++) {
                random -= weights[i];
                if (random <= 0) {
                    return choices[i];
                }
            }
            return choices[choices.length - 1]; // Fallback
        }

        function formatIntensityName(intensity) {
            switch (intensity) {
                case 'very-light':
                    return 'Very Light';
                case 'light':
                    return 'Light';
                case 'moderate':
                    return 'Moderate';
                case 'heavy':
                    return 'Heavy';
                case 'very-heavy':
                    return 'Very Heavy';
                case 'none':
                    return 'None';
                default:
                    return intensity.charAt(0).toUpperCase() + intensity.slice(1);
            }
        }

        function calculateFadeDurations(eventDuration) {
            // Scale fade durations based on event duration
            // 15s event: 3s fade in/out (20% each)
            // 180s event: 30s fade in/out (16.7% each)
            const fadeRatio = 0.2; // 20% of duration for fade in, 20% for fade out
            const fadeDuration = Math.round(eventDuration * fadeRatio);

            // Ensure minimum fade duration of 2 seconds and maximum of 30 seconds
            const minFade = 2000;
            const maxFade = 30000;
            const clampedFade = Math.max(minFade, Math.min(maxFade, fadeDuration));

            return {
                fadeIn: clampedFade,
                fadeOut: clampedFade
            };
        }

        function getIntensityForDuration(duration) {
            // Calculate intensity based on duration percentage
            const maxDuration = rainTiming.maxRainDuration;
            const durationPercentage = (duration / maxDuration) * 100;

            // Define intensity ranges based on duration percentage
            if (durationPercentage <= 20) {
                // 0-20%: Only very-light
                return 'very-light';
            } else if (durationPercentage <= 40) {
                // 21-40%: very-light or light
                const intensities = ['very-light', 'light'];
                const weights = [0.6, 0.4]; // Favor very-light
                return getWeightedRandomChoice(intensities, weights);
            } else if (durationPercentage <= 60) {
                // 41-60%: very-light, light, or moderate
                const intensities = ['very-light', 'light', 'moderate'];
                const weights = [0.3, 0.4, 0.3];
                return getWeightedRandomChoice(intensities, weights);
            } else if (durationPercentage <= 80) {
                // 61-80%: light, moderate, or heavy
                const intensities = ['light', 'moderate', 'heavy'];
                const weights = [0.3, 0.4, 0.3];
                return getWeightedRandomChoice(intensities, weights);
            } else {
                // 81-100%: moderate, heavy, or very-heavy
                const intensities = ['moderate', 'heavy', 'very-heavy'];
                const weights = [0.3, 0.4, 0.3];
                return getWeightedRandomChoice(intensities, weights);
            }
        }

        // Debug functions
        function toggleDebugPanel() {
            const debugPanel = document.getElementById('debugPanel');
            const isExpanded = debugPanel.classList.contains('expanded');

            if (isExpanded) {
                debugPanel.classList.remove('expanded');
                debugPanel.classList.add('collapsed');
            } else {
                debugPanel.classList.remove('collapsed');
                debugPanel.classList.add('expanded');
            }
        }

        function triggerDebugRain() {
            const currentTime = Date.now();

            // If rain is already active, don't trigger another
            if (rainSystem.active || rainSystem.fadingIn || rainSystem.fadingOut) {
                return;
            }

            // Mark as debug triggered
            rainSystem.isDebugTriggered = true;

            // Force start a rain cycle immediately
            rainSystem.nextRainTime = currentTime;
            startRainCycle();

            // Update debug display
            updateDebugDisplay();
        }

        function updateDebugDisplay() {
            const currentTime = Date.now();

            // Update status
            const statusElement = document.getElementById('rainStatus');
            const currentIntensityElement = document.getElementById('currentIntensity');
            const peakIntensityElement = document.getElementById('peakIntensity');
            const eventDurationElement = document.getElementById('eventDuration');
            const timeElapsedElement = document.getElementById('timeElapsed');
            const nextRainInElement = document.getElementById('nextRainIn');
            const fadeDurationElement = document.getElementById('fadeDuration');
            const dropletsCountElement = document.getElementById('dropletsCount');
            const targetRangeElement = document.getElementById('targetRange');
            const currentTargetElement = document.getElementById('currentTarget');
            const triggerButton = document.getElementById('triggerRainBtn');

            if (rainSystem.fadingIn) {
                statusElement.textContent = 'Fading In';
                statusElement.className = 'debug-value active';
            } else if (rainSystem.active) {
                statusElement.textContent = 'Active';
                statusElement.className = 'debug-value active';
            } else if (rainSystem.fadingOut) {
                statusElement.textContent = 'Fading Out';
                statusElement.className = 'debug-value active';
            } else {
                statusElement.textContent = 'Inactive';
                statusElement.className = 'debug-value inactive';
            }

            // Update current intensity
            if (rainSystem.active || rainSystem.fadingIn || rainSystem.fadingOut) {
                currentIntensityElement.textContent = formatIntensityName(rainSystem.intensity);
                currentIntensityElement.className = 'debug-value active';
            } else {
                currentIntensityElement.textContent = 'None';
                currentIntensityElement.className = 'debug-value inactive';
            }

            // Update peak intensity
            peakIntensityElement.textContent = formatIntensityName(rainSystem.peakIntensity);

            // Update event duration
            if (rainSystem.rainDuration > 0) {
                eventDurationElement.textContent = Math.round(rainSystem.rainDuration / 1000) + 's';
            } else {
                eventDurationElement.textContent = '0s';
            }

            // Update time elapsed
            if (rainSystem.eventStartTime > 0 && (rainSystem.active || rainSystem.fadingIn || rainSystem.fadingOut)) {
                const elapsed = Math.max(0, Math.round((currentTime - rainSystem.eventStartTime) / 1000));
                timeElapsedElement.textContent = elapsed + 's';
            } else {
                timeElapsedElement.textContent = '0s';
            }

            // Update next rain countdown
            if (!rainSystem.active && !rainSystem.fadingIn && !rainSystem.fadingOut && rainSystem.nextRainTime > currentTime) {
                const timeUntilNext = Math.max(0, Math.round((rainSystem.nextRainTime - currentTime) / 1000));
                nextRainInElement.textContent = timeUntilNext + 's';
            } else if (rainSystem.active || rainSystem.fadingIn || rainSystem.fadingOut) {
                nextRainInElement.textContent = 'Active';
            } else {
                nextRainInElement.textContent = 'Soon';
            }

            // Update fade duration
            if (rainSystem.fadeInDuration > 0) {
                fadeDurationElement.textContent = Math.round(rainSystem.fadeInDuration / 1000) + 's';
            } else {
                fadeDurationElement.textContent = '--';
            }

            // Update droplets count
            const activeDroplets = countActiveDroplets();
            dropletsCountElement.textContent = activeDroplets + ' / ' + rainSystem.maxDrops;

            // Update target range
            if (rainSystem.active || rainSystem.fadingIn || rainSystem.fadingOut) {
                const targetRange = getTargetDropletRange(rainSystem.intensity);
                targetRangeElement.textContent = targetRange.min + '-' + targetRange.max;
                targetRangeElement.className = 'debug-value active';

                // Update current target
                currentTargetElement.textContent = rainSystem.currentTargetDroplets;
                currentTargetElement.className = 'debug-value active';
            } else {
                targetRangeElement.textContent = '--';
                targetRangeElement.className = 'debug-value inactive';
                currentTargetElement.textContent = '--';
                currentTargetElement.className = 'debug-value inactive';
            }

            // Update button state
            if (rainSystem.active || rainSystem.fadingIn || rainSystem.fadingOut) {
                triggerButton.disabled = true;
                triggerButton.textContent = 'Rain Active';
            } else {
                triggerButton.disabled = false;
                triggerButton.textContent = 'Trigger Rain Event';
            }
        }

        function updateWheatAnimation() {
            const windowWidth = window.innerWidth;
            const centerZone = windowWidth * 0.1; // 10% center zone
            const centerX = windowWidth / 2;

            // Calculate mouse wind effect
            const distanceFromCenter = Math.abs(mouseX - centerX);
            let mouseWindStrength = 0;
            let mouseDirection = 0;

            if (distanceFromCenter > centerZone / 2) {
                mouseDirection = mouseX > centerX ? 1 : -1;
                const maxDistance = (windowWidth / 2) - (centerZone / 2);
                mouseWindStrength = Math.min((distanceFromCenter - centerZone / 2) / maxDistance, 1);
            }

            // Update all gust systems
            updateGustSystems();

            // Update rain system
            updateRainSystem();

            // Update debug display
            updateDebugDisplay();

            // Apply growth scaling and wind effects to each stalk with progressive bending
            stalkData.forEach((stalkInfo, index) => {
                // Apply growth scaling to all segments first
                const scaledBaseHeight = stalkInfo.baseHeight * wheatGrowthFactor;
                const scaledLowerMiddleHeight = stalkInfo.lowerMiddleHeight * wheatGrowthFactor;
                const scaledUpperMiddleHeight = stalkInfo.upperMiddleHeight * wheatGrowthFactor;
                const scaledTopHeight = stalkInfo.topHeight * wheatGrowthFactor;
                const scaledTotalHeight = stalkInfo.height * wheatGrowthFactor;

                // Update segment heights and positions based on growth
                stalkInfo.baseSegment.style.height = scaledBaseHeight + 'px';
                stalkInfo.lowerMiddleSegment.style.height = scaledLowerMiddleHeight + 'px';
                stalkInfo.lowerMiddleSegment.style.bottom = scaledBaseHeight + 'px';
                stalkInfo.upperMiddleSegment.style.height = scaledUpperMiddleHeight + 'px';
                stalkInfo.upperMiddleSegment.style.bottom = (scaledBaseHeight + scaledLowerMiddleHeight) + 'px';
                stalkInfo.topSegment.style.height = scaledTopHeight + 'px';
                stalkInfo.topSegment.style.bottom = (scaledBaseHeight + scaledLowerMiddleHeight + scaledUpperMiddleHeight) + 'px';

                // Hide stalks completely when growth factor is 0 (seed state)
                stalkInfo.element.style.opacity = wheatGrowthFactor === 0 ? '0' : '0.9';

                // Handle berry visibility based on growth factor
                // Berries only appear when wheat is between 80% and 100% growth
                const berryThreshold = 0.8; // 80% growth threshold
                let berryOpacity = 0;

                if (wheatGrowthFactor >= berryThreshold) {
                    // Calculate berry opacity based on growth in the final 20% (0.8 to 1.0)
                    const berryGrowthRange = 1.0 - berryThreshold; // 0.2 (20%)
                    const berryProgress = (wheatGrowthFactor - berryThreshold) / berryGrowthRange;
                    berryOpacity = Math.min(1, berryProgress); // 0 to 1
                }

                // Apply berry opacity to the top segment's ::after pseudo-element
                // We need to use CSS custom properties to control the berry opacity
                stalkInfo.topSegment.style.setProperty('--berry-opacity', berryOpacity);

                // Calculate wind effects using scaled heights for realistic physics
                let totalRotation = 0;

                // Only apply wind effects if wheat has grown (growth factor > 0)
                if (wheatGrowthFactor > 0) {
                    // Mouse wind effect
                    if (mouseWindStrength > 0) {
                        const mouseRotation = mouseDirection * mouseWindStrength * 25 * stalkInfo.flexibility * stalkInfo.naturalSway;
                        totalRotation += mouseRotation;
                    }

                    // Multiple gust wind effects (can stack)
                    gustSystems.forEach(gust => {
                        if (gust.active) {
                            const stalkPosition = stalkInfo.position * 100; // Convert to percentage
                            const distanceFromGust = Math.abs(stalkPosition - gust.position);

                            if (distanceFromGust <= gust.width) {
                                // Stalk is within gust range
                                const gustInfluence = Math.max(0, 1 - (distanceFromGust / gust.width));

                                // Enhanced gust effect with strength variation
                                const baseGustRotation = 35; // Increased base rotation for more dramatic effect
                                const strengthMultiplier = Math.pow(gust.strength, 1.5); // Non-linear strength scaling
                                const gustRotation = gust.direction * strengthMultiplier * baseGustRotation * gustInfluence * stalkInfo.flexibility * stalkInfo.naturalSway;

                                totalRotation += gustRotation;
                            }
                        }
                    });

                    // Add subtle natural sway even without wind
                    const naturalSway = Math.sin(Date.now() * 0.001 + index * 0.1) * 2 * stalkInfo.naturalSway;
                    totalRotation += naturalSway;
                }

                // Calculate progressive bending for realistic physics with chained movement (4 segments)
                const baseRotation = totalRotation * 0.2;         // Base: 20% of total rotation
                const lowerMiddleRotation = totalRotation * 0.4;   // Lower-middle: 40% of total rotation
                const upperMiddleRotation = totalRotation * 0.7;   // Upper-middle: 70% of total rotation
                const topRotation = totalRotation;                 // Top: 100% of total rotation

                // Apply dynamic transition based on wind strength
                const windIntensity = Math.abs(totalRotation);
                const transitionSpeed = Math.max(0.1, Math.min(0.5, 0.3 - (windIntensity * 0.005))); // Faster transitions for stronger winds

                // Calculate chained positions for realistic connected movement (4 segments) using scaled heights
                // Base segment rotates from stalk root
                stalkInfo.baseSegment.style.transform = `rotate(${baseRotation}deg)`;
                stalkInfo.baseSegment.style.transition = `transform ${transitionSpeed}s ease-out`;

                // Calculate where the top of the base segment ends up after rotation (using scaled height)
                const baseRadians = (baseRotation * Math.PI) / 180;
                const baseEndX = Math.sin(baseRadians) * scaledBaseHeight;
                const baseEndY = Math.cos(baseRadians) * scaledBaseHeight;

                // Position lower-middle segment to start from the end of the base segment (using scaled heights)
                const lowerMiddleOriginalY = scaledBaseHeight;
                const lowerMiddleTranslateX = baseEndX;
                const lowerMiddleTranslateY = lowerMiddleOriginalY - baseEndY;

                stalkInfo.lowerMiddleSegment.style.transform = `translate(${lowerMiddleTranslateX}px, ${lowerMiddleTranslateY}px) rotate(${lowerMiddleRotation}deg)`;
                stalkInfo.lowerMiddleSegment.style.transition = `transform ${transitionSpeed}s ease-out`;

                // Calculate where the top of the lower-middle segment ends up after its rotation and translation (using scaled height)
                const lowerMiddleRadians = (lowerMiddleRotation * Math.PI) / 180;
                const lowerMiddleEndX = lowerMiddleTranslateX + Math.sin(lowerMiddleRadians) * scaledLowerMiddleHeight;
                const lowerMiddleEndY = baseEndY + Math.cos(lowerMiddleRadians) * scaledLowerMiddleHeight;

                // Position upper-middle segment to start from the end of the lower-middle segment (using scaled heights)
                const upperMiddleOriginalY = scaledBaseHeight + scaledLowerMiddleHeight;
                const upperMiddleTranslateX = lowerMiddleEndX;
                const upperMiddleTranslateY = upperMiddleOriginalY - lowerMiddleEndY;

                stalkInfo.upperMiddleSegment.style.transform = `translate(${upperMiddleTranslateX}px, ${upperMiddleTranslateY}px) rotate(${upperMiddleRotation}deg)`;
                stalkInfo.upperMiddleSegment.style.transition = `transform ${transitionSpeed}s ease-out`;

                // Calculate where the top of the upper-middle segment ends up after its rotation and translation (using scaled height)
                const upperMiddleRadians = (upperMiddleRotation * Math.PI) / 180;
                const upperMiddleEndX = upperMiddleTranslateX + Math.sin(upperMiddleRadians) * scaledUpperMiddleHeight;
                const upperMiddleEndY = lowerMiddleEndY + Math.cos(upperMiddleRadians) * scaledUpperMiddleHeight;

                // Position top segment to start from the end of the upper-middle segment (using scaled heights)
                const topOriginalY = scaledBaseHeight + scaledLowerMiddleHeight + scaledUpperMiddleHeight;
                const topTranslateX = upperMiddleEndX;
                const topTranslateY = topOriginalY - upperMiddleEndY;

                stalkInfo.topSegment.style.transform = `translate(${topTranslateX}px, ${topTranslateY}px) rotate(${topRotation}deg)`;
                stalkInfo.topSegment.style.transition = `transform ${transitionSpeed}s ease-out`;
            });
        }

        // Animation loop for continuous updates
        function animationLoop() {
            updateWheatAnimation();
            requestAnimationFrame(animationLoop);
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            generateWheatStalks();
            mouseX = window.innerWidth / 2; // Reset horizontal mouse position
            mouseY = window.innerHeight; // Reset vertical mouse position to bottom (full growth)
            wheatGrowthFactor = 1.0; // Reset to full growth

            // Reset all gust systems
            gustSystems.forEach((gust, index) => {
                gust.active = false;
                gust.nextGustTime = Date.now() + 2000 + (index * gustFrequency.minStagger);
            });
            gustFrequency.lastGustTime = 0;

            // Reset rain system on resize
            if (rainSystem.active || rainSystem.fadingIn || rainSystem.fadingOut) {
                endRainCycle();
            }
        });

        // Initialize wheat field and rain system
        document.addEventListener('DOMContentLoaded', function() {
            generateWheatStalks();
            initializeRainSystem();

            // Set initial gust timing with staggered starts
            const currentTime = Date.now();
            gustSystems.forEach((gust, index) => {
                const baseDelay = Math.random() * 4000 + 2000; // 2-6 seconds initial delay
                gust.nextGustTime = currentTime + baseDelay + (index * gustFrequency.minStagger);
            });
            gustFrequency.lastGustTime = currentTime;

            // Start animation loop
            animationLoop();
        });
    </script>
</body>
</html>
